#!/bin/bash

# Remnawave Backup and Restore Functionality

BACKUP_INSTALL_DIR="/opt/rw-backup-restore"
BACKUP_DIR="$BACKUP_INSTALL_DIR/backup"
BACKUP_CONFIG_FILE="$BACKUP_INSTALL_DIR/config.env"
BACKUP_SCRIPT_NAME="rw-backup.sh"
BACKUP_SCRIPT_PATH="$BACKUP_INSTALL_DIR/$BACKUP_SCRIPT_NAME"
RETAIN_BACKUPS_DAYS=7
SYMLINK_PATH="/usr/local/bin/rw-backup"

# File paths for .env files
ENV_NODE_FILE=".env-node"
ENV_FILE=".env"
UPLOAD_METHOD="telegram"

# Initialize variables that will be loaded from config
BOT_TOKEN=""
CHAT_ID=""
DB_USER="postgres"
GD_CLIENT_ID=""
GD_CLIENT_SECRET=""
GD_REFRESH_TOKEN=""
GD_FOLDER_ID=""
CRON_TIMES=""

# Main menu for backup and restore functionality
backup_restore_menu() {
    while true; do
        clear_screen
        draw_section_header "$(t backup_menu_title)"
        
        draw_menu_options "
1) $(t backup_menu_create)
2) $(t backup_menu_restore)
3) $(t backup_menu_configure)
4) $(t backup_menu_schedule)
0) $(t backup_menu_back)"

        local choice=$(prompt_menu_option 0 4)
        case $choice in
            1) create_backup ;;
            2) restore_backup ;;
            3) load_or_create_config ;;
            4) setup_auto_send ;;
            0) return ;;
        esac
    done
}

# Function to send notification messages via Telegram
send_telegram_message() {
    local message="$1"
    local parse_mode="${2:-MarkdownV2}"
    local escaped_message
    
    if [[ "$parse_mode" == "MarkdownV2" ]]; then
        escaped_message=$(escape_markdown_v2 "$message")
    else
        escaped_message="$message"
    fi

    if [[ -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        show_error "$(t backup_config_telegram_bot) $(t backup_config_telegram_id)"
        return 1
    fi

    local http_code=$(curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" \
        -d chat_id="$CHAT_ID" \
        -d text="$escaped_message" \
        -d parse_mode="$parse_mode" \
        -w "%{http_code}" -o /dev/null 2>&1)

    if [[ "$http_code" -eq 200 ]]; then
        return 0
    else
        show_error "Failed to send Telegram message. HTTP code: $http_code"
        return 1
    fi
}

# Function to escape special characters for Telegram MarkdownV2 format
escape_markdown_v2() {
    local text="$1"
    echo "$text" | sed \
        -e 's/\\/\\\\/g' \
        -e 's/_/\\_/g' \
        -e 's/\[/\\[/g' \
        -e 's/\]/\\]/g' \
        -e 's/(/\\(/g' \
        -e 's/)/\\)/g' \
        -e 's/~/\\~/g' \
        -e 's/`/\\`/g' \
        -e 's/>/\\>/g' \
        -e 's/#/\\#/g' \
        -e 's/+/\\+/g' \
        -e 's/-/\\-/g' \
        -e 's/=/\\=/g' \
        -e 's/|/\\|/g' \
        -e 's/{/\\{/g' \
        -e 's/}/\\}/g' \
        -e 's/\./\\./g' \
        -e 's/!/\\!/g'
}

# Function to send files via Telegram
send_telegram_document() {
    local file_path="$1"
    local caption="$2"
    local parse_mode="MarkdownV2"
    local escaped_caption
    escaped_caption=$(escape_markdown_v2 "$caption")

    if [[ -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        show_error "$(t backup_config_telegram_bot) $(t backup_config_telegram_id)"
        return 1
    fi

    local http_code=$(curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendDocument" \
        -F chat_id="$CHAT_ID" \
        -F document=@"$file_path" \
        -F parse_mode="$parse_mode" \
        -F caption="$escaped_caption" \
        -w "%{http_code}" -o /dev/null 2>&1)

    if [[ "$http_code" == "200" ]]; then
        return 0
    else
        show_error "Failed to send document to Telegram. HTTP code: $http_code"
        return 1
    fi
}

# Function to create necessary directories
create_directories() {
    mkdir -p "$BACKUP_INSTALL_DIR"
    mkdir -p "$BACKUP_DIR"
    chown -R $USER:$USER "$BACKUP_INSTALL_DIR"
}

# Function to initialize the backup system
initialize_backup() {
    # Create required directories
    create_directories
    
    # Ensure Docker is installed and running
    if ! command -v docker &>/dev/null; then
        show_error "Docker not installed. Please install Docker first."
        return 1
    fi
    
    # Check if required tools are available
    for cmd in curl gzip tar jq; do
        if ! command -v $cmd &>/dev/null; then
            show_error "Required command '$cmd' not found. Please install it."
            return 1
        fi
    done
    
    # Create symlink for easy access
    ln -sf "$BACKUP_SCRIPT_PATH" "$SYMLINK_PATH" 2>/dev/null
    
    return 0
}

# Function to save configuration
save_config() {
    # Create the config directory if it doesn't exist
    mkdir -p "$(dirname "$BACKUP_CONFIG_FILE")" 2>/dev/null
    
    # Write config values to file
    cat > "$BACKUP_CONFIG_FILE" << EOF
BOT_TOKEN="$BOT_TOKEN"
CHAT_ID="$CHAT_ID"
DB_USER="$DB_USER"
UPLOAD_METHOD="$UPLOAD_METHOD"
GD_CLIENT_ID="$GD_CLIENT_ID"
GD_CLIENT_SECRET="$GD_CLIENT_SECRET"
GD_REFRESH_TOKEN="$GD_REFRESH_TOKEN"
GD_FOLDER_ID="$GD_FOLDER_ID"
CRON_TIMES="$CRON_TIMES"
RETAIN_BACKUPS_DAYS="$RETAIN_BACKUPS_DAYS"
EOF
    
    # Set appropriate permissions
    chmod 600 "$BACKUP_CONFIG_FILE"
    
    show_info "$(t backup_config_saved)"
}

# Function to load configuration
load_config() {
    if [[ -f "$BACKUP_CONFIG_FILE" ]]; then
        # Source the config file
        # shellcheck disable=SC1090
        source "$BACKUP_CONFIG_FILE"
        return 0
    else
        return 1
    fi
}

# Function to prompt for configuration settings
prompt_for_config() {
    clear_screen
    draw_section_header "$(t backup_config_method)"
    
    echo "1) $(t backup_config_telegram)"
    echo "2) $(t backup_config_google)"
    
    local choice=$(prompt_menu_option 1 2)
    
    if [[ "$choice" == "1" ]]; then
        UPLOAD_METHOD="telegram"
        clear_screen
        
        draw_section_header "$(t backup_config_telegram)"
        
        echo -e "$(t backup_config_telegram_bot):"
        read -r BOT_TOKEN
        
        echo -e "$(t backup_config_telegram_id):"
        read -r CHAT_ID
    else
        UPLOAD_METHOD="gdrive"
        clear_screen
        
        draw_section_header "$(t backup_config_google)"
        
        echo -e "Google Drive Client ID:"
        read -r GD_CLIENT_ID
        
        echo -e "Google Drive Client Secret:"
        read -r GD_CLIENT_SECRET
        
        echo -e "Google Drive Refresh Token (optional):"
        read -r GD_REFRESH_TOKEN
        
        echo -e "Google Drive Folder ID (optional):"
        read -r GD_FOLDER_ID
    fi
    
    save_config
}

# Function to load or create configuration
load_or_create_config() {
    if ! load_config; then
        prompt_for_config
    else
        prompt_for_config
    fi
}

# Function to create a backup
create_backup() {
    clear_screen
    draw_section_header "Creating backup"
    
    # Initialize backup
    if ! initialize_backup; then
        show_error "Failed to initialize backup system"
        prompt_for_enter
        return 1
    fi
    
    # Load configuration if exists
    load_config
    
    # Generate timestamp for backup file
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="remnawave_backup_${timestamp}.tar.gz"
    local backup_path="$BACKUP_DIR/$backup_file"
    
    # Create temporary directory
    local temp_dir=$(mktemp -d)
    local db_dump="$temp_dir/database.sql"
    local env_dir="$temp_dir/env"
    
    # Create directory for env files
    mkdir -p "$env_dir"
    
    show_info "Exporting database..."
    # Export PostgreSQL database from Docker container
    if ! docker exec remnawave-db pg_dump -U $DB_USER -d remnawave > "$db_dump" 2>/dev/null; then
        show_error "Failed to export database"
        rm -rf "$temp_dir"
        prompt_for_enter
        return 1
    fi
    
    # Find and copy .env files
    show_info "Copying environment files..."
    
    # Check for panel .env file and copy if exists
    if [[ -f "$REMNAWAVE_DIR/$ENV_FILE" ]]; then
        cp "$REMNAWAVE_DIR/$ENV_FILE" "$env_dir/" 2>/dev/null
    fi
    
    # Check for node .env file and copy if exists
    if [[ -f "$REMNAWAVE_DIR/$ENV_NODE_FILE" ]]; then
        cp "$REMNAWAVE_DIR/$ENV_NODE_FILE" "$env_dir/" 2>/dev/null
    fi
    
    # Create archive of all backup files
    show_info "Creating backup archive..."
    if ! tar -czf "$backup_path" -C "$temp_dir" . >/dev/null 2>&1; then
        show_error "Failed to create backup archive"
        rm -rf "$temp_dir"
        prompt_for_enter
        return 1
    fi
    
    # Clean up temporary directory
    rm -rf "$temp_dir"
    
    # Clean up old backups (older than specified days)
    find "$BACKUP_DIR" -name "remnawave_backup_*.tar.gz" -type f -mtime +"$RETAIN_BACKUPS_DAYS" -delete 2>/dev/null
    
    # Send backup to the configured method if available
    if [[ "$UPLOAD_METHOD" == "telegram" && -n "$BOT_TOKEN" && -n "$CHAT_ID" ]]; then
        show_info "Sending backup via Telegram..."
        local server_name=$(hostname)
        local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")
        local caption="Remnawave Backup\nServer: $server_name\nIP: $server_ip\nDate: $(date '+%Y-%m-%d %H:%M:%S')"
        
        if send_telegram_document "$backup_path" "$caption"; then
            show_success "Backup sent via Telegram"
        else
            show_error "Failed to send backup via Telegram"
        fi
    elif [[ "$UPLOAD_METHOD" == "gdrive" && -n "$GD_CLIENT_ID" && -n "$GD_CLIENT_SECRET" ]]; then
        show_info "Google Drive upload not yet implemented"
        # Future implementation for Google Drive
    fi
    
    show_success "$(t backup_success): $backup_path"
    prompt_for_enter
    return 0
}

# Function to list available backups
list_backups() {
    local backups=()
    local i=0
    
    # Check if backup directory exists
    if [[ ! -d "$BACKUP_DIR" ]]; then
        return 1
    fi
    
    # Find and list all backup files
    while IFS= read -r file; do
        backups[i++]="$file"
    done < <(find "$BACKUP_DIR" -name "remnawave_backup_*.tar.gz" -type f -printf "%T@ %p\n" | sort -nr | cut -d' ' -f2-)
    
    # Return 1 if no backups found
    if [[ "${#backups[@]}" -eq 0 ]]; then
        return 1
    fi
    
    # Display available backups
    echo "Available backups:"
    for i in "${!backups[@]}"; do
        local file="${backups[$i]}"
        local date=$(date -r "$file" "+%Y-%m-%d %H:%M:%S")
        local size=$(du -h "$file" | cut -f1)
        local filename=$(basename "$file")
        echo "$((i+1))) $filename ($date, $size)"
    done
    
    # Return the array of backup files
    echo "${backups[@]}"
}

# Function to restore from a backup
restore_backup() {
    clear_screen
    draw_section_header "Restoring from backup"
    
    # Initialize backup system
    if ! initialize_backup; then
        show_error "Failed to initialize backup system"
        prompt_for_enter
        return 1
    fi
    
    # Get list of available backups
    local backups_output=$(list_backups)
    
    if [[ $? -ne 0 ]]; then
        show_error "$(t backup_no_backups)"
        prompt_for_enter
        return 1
    fi
    
    # Extract number of backups
    local num_lines=$(echo "$backups_output" | wc -l)
    local num_backups=$((num_lines - 1))
    
    # Get array of backup files from last line
    local IFS=' '
    local backups=($(echo "$backups_output" | tail -n1))
    
    # Prompt user to select a backup
    echo ""
    echo "0) $(t backup_menu_back)"
    local choice=$(prompt_menu_option 0 $num_backups)
    
    if [[ "$choice" -eq 0 ]]; then
        return 0
    fi
    
    local selected_backup="${backups[$((choice-1))]}"
    
    # Warning before restore
    clear_screen
    draw_section_header "WARNING"
    echo -e "${RED}This will restore the database and configuration files.${NC}"
    echo -e "${YELLOW}All current data will be replaced with data from the backup.${NC}"
    echo -e "${YELLOW}The panel will be restarted during restoration.${NC}"
    
    echo ""
    if ! prompt_yn "Continue with restore?"; then
        return 0
    fi
    
    # Create temporary directory for extraction
    local temp_dir=$(mktemp -d)
    
    # Extract the backup archive
    show_info "Extracting backup..."
    if ! tar -xzf "$selected_backup" -C "$temp_dir" >/dev/null 2>&1; then
        show_error "Failed to extract backup archive"
        rm -rf "$temp_dir"
        prompt_for_enter
        return 1
    fi
    
    # Validate backup contents
    if [[ ! -f "$temp_dir/database.sql" ]]; then
        show_error "Invalid backup: Missing database dump"
        rm -rf "$temp_dir"
        prompt_for_enter
        return 1
    fi
    
    # Stop panel services
    show_info "Stopping Remnawave services..."
    docker-compose -f "$REMNAWAVE_DIR/docker-compose.yml" down >/dev/null 2>&1
    
    # Restore database
    show_info "Restoring database..."
    docker start remnawave-db >/dev/null 2>&1
    
    # Wait for database to start
    sleep 5
    
    if ! docker exec -i remnawave-db psql -U $DB_USER -d remnawave < "$temp_dir/database.sql" >/dev/null 2>&1; then
        show_error "Failed to restore database"
        docker-compose -f "$REMNAWAVE_DIR/docker-compose.yml" up -d >/dev/null 2>&1
        rm -rf "$temp_dir"
        prompt_for_enter
        return 1
    fi
    
    # Restore env files if they exist in the backup
    if [[ -d "$temp_dir/env" ]]; then
        show_info "Restoring environment files..."
        
        # Restore panel .env if exists
        if [[ -f "$temp_dir/env/$ENV_FILE" ]]; then
            cp "$temp_dir/env/$ENV_FILE" "$REMNAWAVE_DIR/$ENV_FILE" 2>/dev/null
        fi
        
        # Restore node .env if exists
        if [[ -f "$temp_dir/env/$ENV_NODE_FILE" ]]; then
            cp "$temp_dir/env/$ENV_NODE_FILE" "$REMNAWAVE_DIR/$ENV_NODE_FILE" 2>/dev/null
        fi
    fi
    
    # Restart panel services
    show_info "Starting Remnawave services..."
    docker-compose -f "$REMNAWAVE_DIR/docker-compose.yml" up -d >/dev/null 2>&1
    
    # Clean up
    rm -rf "$temp_dir"
    
    show_success "$(t backup_restore_success)"
    prompt_for_enter
    return 0
}

# Function to setup automatic backup scheduling
setup_auto_send() {
    # Initialize backup
    if ! initialize_backup; then
        show_error "Failed to initialize backup system"
        prompt_for_enter
        return 1
    fi
    
    # Load configuration if exists
    load_config
    
    clear_screen
    draw_section_header "Automatic Backup Configuration"
    
    # Check current cron status
    local cron_exists=0
    if crontab -l | grep -q "$BACKUP_SCRIPT_PATH" 2>/dev/null; then
        cron_exists=1
    fi
    
    if [[ "$cron_exists" -eq 1 ]]; then
        echo -e "${GREEN}$(t backup_cron_enabled)${NC}"
        echo -e "${YELLOW}$(t backup_cron_time): ${CRON_TIMES:-"00 00 * * *"}${NC}"
        echo ""
        echo "1) $(t backup_cron_set)"
        echo "2) $(t backup_cron_disable)"
        echo "0) $(t backup_menu_back)"
        
        local choice=$(prompt_menu_option 0 2)
        
        case $choice in
            0) return ;;
            1) setup_cron ;;
            2) disable_cron ;;
        esac
    else
        echo -e "${RED}$(t backup_cron_disabled)${NC}"
        echo ""
        echo "1) $(t backup_cron_set)"
        echo "0) $(t backup_menu_back)"
        
        local choice=$(prompt_menu_option 0 1)
        
        case $choice in
            0) return ;;
            1) setup_cron ;;
        esac
    fi
}

# Function to setup cron job
setup_cron() {
    clear_screen
    draw_section_header "Setup Backup Schedule"
    
    echo "Select backup schedule (UTC+0):"
    echo "1) Daily (00:00)"
    echo "2) Daily (12:00)"
    echo "3) Weekly (Sunday 00:00)"
    echo "4) Monthly (1st day 00:00)"
    echo "5) Custom schedule"
    
    local choice=$(prompt_menu_option 1 5)
    
    case $choice in
        1) CRON_TIMES="0 0 * * *" ;;
        2) CRON_TIMES="0 12 * * *" ;;
        3) CRON_TIMES="0 0 * * 0" ;;
        4) CRON_TIMES="0 0 1 * *" ;;
        5)
            echo "Enter custom cron schedule (format: minute hour day month weekday):"
            read -r CRON_TIMES
            ;;
    esac
    
    # Update crontab
    (crontab -l 2>/dev/null | grep -v "$BACKUP_SCRIPT_PATH" || true; echo "$CRON_TIMES $BACKUP_SCRIPT_PATH --create --silent") | crontab -
    
    # Save configuration
    save_config
    
    show_success "$(t backup_cron_success)"
    prompt_for_enter
}

# Function to disable cron job
disable_cron() {
    # Remove cron job
    crontab -l 2>/dev/null | grep -v "$BACKUP_SCRIPT_PATH" | crontab -
    
    # Update configuration
    CRON_TIMES=""
    save_config
    
    show_success "$(t backup_cron_disabled)"
    prompt_for_enter
}
